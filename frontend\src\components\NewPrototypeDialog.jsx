import { useState } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  Box,
  Typography,
  IconButton
} from '@mui/material';
import { Close as CloseIcon } from '@mui/icons-material';

function NewPrototypeDialog({ open, onClose, onCreatePrototype }) {
  const [prototypeName, setPrototypeName] = useState('');
  const [error, setError] = useState('');

  const handleSubmit = (e) => {
    e.preventDefault();

    if (!prototypeName.trim()) {
      setError('Please enter a name for your prototype');
      return;
    }

    onCreatePrototype(prototypeName);
    setPrototypeName('');
    setError('');
    onClose();
  };

  const handleClose = () => {
    setPrototypeName('');
    setError('');
    onClose();
  };

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
          backgroundColor: '#1A1A2E',
          backgroundImage: 'linear-gradient(135deg, rgba(139, 92, 246, 0.1) 0%, rgba(30, 30, 60, 0.1) 100%)',
        }
      }}
    >
      <DialogTitle
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          borderBottom: '1px solid rgba(75, 85, 99, 0.2)',
          pb: 1,
          color: 'white',
          fontWeight: 600
        }}
      >
        Create New Prototype
        <IconButton onClick={handleClose} size="small" sx={{ color: 'rgba(255, 255, 255, 0.7)' }}>
          <CloseIcon fontSize="small" />
        </IconButton>
      </DialogTitle>

      <form onSubmit={handleSubmit}>
        <DialogContent sx={{ pt: 3 }}>
          <Typography variant="body2" sx={{ mb: 2, color: 'rgba(255, 255, 255, 0.7)' }}>
            Give your prototype a name to help you identify it later.
          </Typography>

          <TextField
            autoFocus
            label="Prototype Name"
            fullWidth
            value={prototypeName}
            onChange={(e) => {
              setPrototypeName(e.target.value);
              if (e.target.value.trim()) setError('');
            }}
            error={!!error}
            helperText={error}
            variant="outlined"
            sx={{
              '& .MuiOutlinedInput-root': {
                borderRadius: 1,
                backgroundColor: 'rgba(30, 30, 60, 0.6)',
                '& fieldset': {
                  borderColor: 'rgba(168, 85, 247, 0.3)',
                },
                '&:hover fieldset': {
                  borderColor: 'rgba(168, 85, 247, 0.5)',
                },
                '&.Mui-focused fieldset': {
                  borderColor: 'rgba(168, 85, 247, 0.8)',
                },
              },
              '& .MuiInputBase-input': {
                color: 'rgba(255, 255, 255, 0.9)',
              },
              '& .MuiInputLabel-root': {
                color: 'rgba(255, 255, 255, 0.7)',
              },
              '& .MuiFormHelperText-root': {
                color: '#F87171',
              }
            }}
          />
        </DialogContent>

        <DialogActions sx={{ px: 3, pb: 3 }}>
          <Button
            onClick={handleClose}
            variant="outlined"
            sx={{
              borderColor: 'rgba(168, 85, 247, 0.3)',
              color: 'rgba(255, 255, 255, 0.9)',
              '&:hover': {
                borderColor: 'rgba(168, 85, 247, 0.8)',
                backgroundColor: 'rgba(168, 85, 247, 0.1)'
              }
            }}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            variant="contained"
            sx={{
              backgroundColor: '#8B5CF6',
              '&:hover': {
                backgroundColor: '#7C3AED',
              },
              fontWeight: 600,
              px: 3
            }}
          >
            Create
          </Button>
        </DialogActions>
      </form>
    </Dialog>
  );
}

export default NewPrototypeDialog;
