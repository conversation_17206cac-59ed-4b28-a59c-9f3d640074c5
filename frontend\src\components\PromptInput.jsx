import { useState } from 'react';
import {
  Paper,
  Typography,
  TextField,
  Button,
  Box,
  Chip,
  CircularProgress,
  Stack,
  IconButton,
  Tooltip,
  List,
  ListItem,
  ListItemText,
  Avatar
} from '@mui/material';
import {
  Info as InfoIcon,
  Person as PersonIcon,
  Code as CodeIcon,
  Send as SendIcon
} from '@mui/icons-material';

function PromptInput({ onGenerate, isGenerating, conversationMode = false, conversationHistory = [], currentConversation = null }) {
  const [prompt, setPrompt] = useState('');
  const [examples] = useState([
    'Create a landing page for a fitness app with a hero section, features list, and pricing table',
    'Design a dashboard for a project management tool with sidebar navigation, task list, and calendar',
    'Build a product page for an e-commerce site with product images, description, and add to cart button'
  ]);

  const handleSubmit = (e) => {
    e.preventDefault();
    if (!prompt.trim()) return;

    // If in conversation mode and we have a current conversation, this is a follow-up prompt
    const isFollowUp = conversationMode && currentConversation;
    onGenerate(prompt, isFollowUp);
    setPrompt(''); // Clear the prompt after submission
  };

  const handleExampleClick = (example) => {
    setPrompt(example);
  };

  return (
    <Paper
      elevation={0}
      sx={{
        borderRadius: 1,
        display: 'flex',
        flexDirection: 'column',
        height: 'auto',
        overflow: 'hidden',
        backgroundColor: 'transparent',
      }}
    >
      <Box
        sx={{
          p: 1,
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          borderBottom: '1px solid rgba(75, 85, 99, 0.2)',
          mb: 1
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Typography variant="subtitle2" fontWeight={600} sx={{ color: 'rgba(255, 255, 255, 0.9)' }}>
            Conversation History
          </Typography>
        </Box>
        <Tooltip title="Learn how to write effective prompts">
          <IconButton size="small" sx={{ color: 'rgba(255, 255, 255, 0.6)' }}>
            <InfoIcon fontSize="small" />
          </IconButton>
        </Tooltip>
      </Box>

      <Box sx={{ p: 1 }}>
        {conversationMode && conversationHistory && conversationHistory.length > 0 ? (
          <List sx={{
            maxHeight: '200px',
            overflowY: 'auto',
            borderRadius: 1,
            mb: 1.5,
            p: 0,
            backgroundColor: 'rgba(30, 30, 60, 0.4)'
          }}>
            {conversationHistory.map((message, index) => (
              <ListItem
                key={index}
                alignItems="flex-start"
                sx={{
                  py: 1,
                  px: 1.5,
                  borderBottom: index < conversationHistory.length - 1 ? '1px solid rgba(75, 85, 99, 0.2)' : 'none',
                  backgroundColor: message.role === 'user' ? 'transparent' : 'rgba(168, 85, 247, 0.05)'
                }}
              >
                <Avatar
                  sx={{
                    width: 24,
                    height: 24,
                    mr: 1,
                    bgcolor: message.role === 'user' ? '#8B5CF6' : '#EC4899',
                    fontSize: '0.75rem'
                  }}
                >
                  {message.role === 'user' ? <PersonIcon fontSize="small" /> : <CodeIcon fontSize="small" />}
                </Avatar>
                <ListItemText
                  primary={
                    <Typography variant="body2" fontWeight={600} sx={{ color: 'rgba(255, 255, 255, 0.9)', fontSize: '0.8rem' }}>
                      {message.role === 'user' ? 'You' : 'AI'}
                    </Typography>
                  }
                  secondary={
                    <Typography
                      variant="body2"
                      sx={{
                        color: 'rgba(255, 255, 255, 0.7)',
                        whiteSpace: 'nowrap',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        maxWidth: '100%',
                        fontSize: '0.75rem'
                      }}
                    >
                      {message.role === 'user'
                        ? message.content
                        : 'Generated HTML code'}
                    </Typography>
                  }
                />
              </ListItem>
            ))}
          </List>
        ) : (
          <Box sx={{
            p: 2,
            textAlign: 'center',
            color: 'rgba(255, 255, 255, 0.5)',
            backgroundColor: 'rgba(30, 30, 60, 0.4)',
            borderRadius: 1,
            mb: 1.5
          }}>
            <Typography variant="body2" sx={{ fontSize: '0.8rem' }}>
              No conversation history yet.
            </Typography>
            <Typography variant="body2" sx={{ fontSize: '0.8rem', mt: 0.5 }}>
              Start by entering a prompt below.
            </Typography>
          </Box>
        )}

        <form onSubmit={handleSubmit} style={{ display: 'flex', flexDirection: 'column', flexGrow: 1 }}>
          <TextField
            id="prompt"
            placeholder="Create a landing page for a fitness app with a hero section..."
            multiline
            rows={3}
            fullWidth
            value={prompt}
            onChange={(e) => setPrompt(e.target.value)}
            variant="outlined"
            sx={{
              mb: 1.5,
              '& .MuiOutlinedInput-root': {
                borderRadius: 1,
                backgroundColor: 'rgba(30, 30, 60, 0.6)',
                '& fieldset': {
                  borderColor: 'rgba(168, 85, 247, 0.3)',
                },
                '&:hover fieldset': {
                  borderColor: 'rgba(168, 85, 247, 0.5)',
                },
                '&.Mui-focused fieldset': {
                  borderColor: 'rgba(168, 85, 247, 0.8)',
                },
              },
              '& .MuiInputBase-input': {
                color: 'rgba(255, 255, 255, 0.9)',
              }
            }}
          />

          <Box sx={{ mb: 1.5 }}>
            <Typography variant="body2" sx={{ color: 'rgba(255, 255, 255, 0.7)', mb: 0.5, fontSize: '0.75rem' }}>
              Examples:
            </Typography>
            <Stack direction="column" spacing={0.5}>
              {examples.map((example, index) => (
                <Chip
                  key={index}
                  label={example.length > 40 ? example.substring(0, 40) + '...' : example}
                  onClick={() => handleExampleClick(example)}
                  variant="outlined"
                  size="small"
                  sx={{
                    borderRadius: 1,
                    height: 'auto',
                    py: 0.5,
                    fontSize: '0.75rem',
                    backgroundColor: 'rgba(30, 30, 60, 0.6)',
                    borderColor: 'rgba(168, 85, 247, 0.3)',
                    color: 'rgba(255, 255, 255, 0.8)',
                    '&:hover': {
                      backgroundColor: 'rgba(168, 85, 247, 0.1)',
                      borderColor: '#A855F7'
                    }
                  }}
                />
              ))}
            </Stack>
          </Box>

          <Button
            type="submit"
            variant="contained"
            fullWidth
            disabled={isGenerating || !prompt.trim()}
            endIcon={isGenerating ? <CircularProgress size={16} color="inherit" /> : <SendIcon />}
            sx={{
              py: 1,
              borderRadius: 1,
              fontWeight: 600,
              backgroundColor: '#8B5CF6',
              '&:hover': {
                backgroundColor: '#7C3AED',
              },
              fontSize: '0.85rem'
            }}
          >
            {isGenerating
              ? 'Generating...'
              : currentConversation
                ? 'Update Prototype'
                : 'Generate Prototype'
            }
          </Button>
        </form>
      </Box>
    </Paper>
  );
}

export default PromptInput;
