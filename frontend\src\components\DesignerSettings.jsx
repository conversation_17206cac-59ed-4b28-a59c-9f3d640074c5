import { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Typography,
  Box,
  Alert,
  IconButton,
  Tooltip,
  Divider,
  Grid,
  Paper,
  RadioGroup,
  FormControlLabel,
  Radio,
  Chip,
  Stack,
  Slider,
  Switch,
} from '@mui/material';
import {
  Palette as PaletteIcon,
  Close as CloseIcon,
  Check as CheckIcon,
  ViewQuilt as ViewQuiltIcon,
  ColorLens as ColorLensIcon,
  FormatSize as FormatSizeIcon,
  Devices as DevicesIcon,
} from '@mui/icons-material';
import { llmService } from '../services/llmService';

// Design style presets
const DESIGN_STYLES = [
  { 
    id: 'minimal', 
    name: 'Minimal', 
    description: 'Clean, simple design with ample white space',
    colors: ['#ffffff', '#f8fafc', '#e2e8f0', '#64748b']
  },
  { 
    id: 'modern', 
    name: 'Modern', 
    description: 'Contemporary design with bold elements',
    colors: ['#0f172a', '#1e293b', '#3b82f6', '#f8fafc']
  },
  { 
    id: 'playful', 
    name: 'Playful', 
    description: 'Fun, colorful design with rounded elements',
    colors: ['#ffffff', '#fef2f2', '#f87171', '#7c3aed']
  },
  { 
    id: 'corporate', 
    name: 'Corporate', 
    description: 'Professional design for business applications',
    colors: ['#ffffff', '#f8fafc', '#0369a1', '#0c4a6e']
  },
  { 
    id: 'luxury', 
    name: 'Luxury', 
    description: 'Elegant design with premium feel',
    colors: ['#0f172a', '#1e293b', '#fbbf24', '#f8fafc']
  },
];

// Layout options
const LAYOUT_OPTIONS = [
  { id: 'single-column', name: 'Single Column', description: 'Content in a single vertical column' },
  { id: 'two-column', name: 'Two Column', description: 'Content split into two columns' },
  { id: 'sidebar', name: 'Sidebar', description: 'Main content with a sidebar' },
  { id: 'card-grid', name: 'Card Grid', description: 'Content organized in a grid of cards' },
  { id: 'hero-sections', name: 'Hero Sections', description: 'Large hero sections with content blocks' },
];

// Color schemes
const COLOR_SCHEMES = [
  { id: 'light', name: 'Light', description: 'Light background with dark text' },
  { id: 'dark', name: 'Dark', description: 'Dark background with light text' },
  { id: 'colorful', name: 'Colorful', description: 'Vibrant, colorful design' },
  { id: 'monochrome', name: 'Monochrome', description: 'Single color with different shades' },
  { id: 'pastel', name: 'Pastel', description: 'Soft, pastel color palette' },
];

// Typography styles
const TYPOGRAPHY_STYLES = [
  { id: 'modern-sans', name: 'Modern Sans', description: 'Clean, modern sans-serif fonts' },
  { id: 'classic-serif', name: 'Classic Serif', description: 'Traditional serif fonts' },
  { id: 'playful-mix', name: 'Playful Mix', description: 'Fun combination of fonts' },
  { id: 'minimal-mono', name: 'Minimal Mono', description: 'Clean monospace fonts' },
  { id: 'elegant-display', name: 'Elegant Display', description: 'Sophisticated display fonts' },
];

function DesignerSettings({ open, onClose, settings, onSave }) {
  const [designStyle, setDesignStyle] = useState(settings?.designStyle || 'minimal');
  const [layoutOption, setLayoutOption] = useState(settings?.layoutOption || 'single-column');
  const [colorScheme, setColorScheme] = useState(settings?.colorScheme || 'light');
  const [typographyStyle, setTypographyStyle] = useState(settings?.typographyStyle || 'modern-sans');
  const [responsiveDesign, setResponsiveDesign] = useState(settings?.responsiveDesign !== false);
  const [animationsLevel, setAnimationsLevel] = useState(settings?.animationsLevel || 1);
  const [saveSuccess, setSaveSuccess] = useState(false);
  const [error, setError] = useState('');

  // Reset state when dialog opens
  useEffect(() => {
    if (open) {
      setDesignStyle(settings?.designStyle || 'minimal');
      setLayoutOption(settings?.layoutOption || 'single-column');
      setColorScheme(settings?.colorScheme || 'light');
      setTypographyStyle(settings?.typographyStyle || 'modern-sans');
      setResponsiveDesign(settings?.responsiveDesign !== false);
      setAnimationsLevel(settings?.animationsLevel || 1);
      setSaveSuccess(false);
      setError('');
    }
  }, [open, settings]);

  const handleSave = () => {
    try {
      const newSettings = {
        designStyle,
        layoutOption,
        colorScheme,
        typographyStyle,
        responsiveDesign,
        animationsLevel,
      };

      onSave(newSettings);
      setSaveSuccess(true);
      setError('');

      // Reset success message after 3 seconds
      setTimeout(() => {
        setSaveSuccess(false);
      }, 3000);
    } catch (err) {
      setError('Failed to save design settings: ' + err.message);
    }
  };

  const getDesignStyleInfo = (styleId) => {
    return DESIGN_STYLES.find(style => style.id === styleId) || DESIGN_STYLES[0];
  };

  const getLayoutOptionInfo = (layoutId) => {
    return LAYOUT_OPTIONS.find(layout => layout.id === layoutId) || LAYOUT_OPTIONS[0];
  };

  const getColorSchemeInfo = (schemeId) => {
    return COLOR_SCHEMES.find(scheme => scheme.id === schemeId) || COLOR_SCHEMES[0];
  };

  const getTypographyStyleInfo = (typographyId) => {
    return TYPOGRAPHY_STYLES.find(typography => typography.id === typographyId) || TYPOGRAPHY_STYLES[0];
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.2)',
        }
      }}
    >
      <DialogTitle sx={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        pb: 1
      }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <PaletteIcon sx={{ mr: 1, color: 'primary.main' }} />
          <Typography variant="h6" component="div">
            Design Settings
          </Typography>
        </Box>
        <IconButton edge="end" color="inherit" onClick={onClose} aria-label="close">
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <Divider />

      <DialogContent sx={{ pt: 3 }}>
        {saveSuccess && (
          <Alert
            icon={<CheckIcon fontSize="inherit" />}
            severity="success"
            sx={{ mb: 3 }}
          >
            Design settings saved successfully!
          </Alert>
        )}

        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        <Grid container spacing={3}>
          {/* Design Style Selection */}
          <Grid item xs={12}>
            <Box sx={{ mb: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <ViewQuiltIcon sx={{ mr: 1, color: 'primary.main' }} />
                <Typography variant="subtitle1" fontWeight={600}>
                  Design Style
                </Typography>
              </Box>
              
              <Grid container spacing={2}>
                {DESIGN_STYLES.map((style) => (
                  <Grid item xs={6} sm={4} md={2.4} key={style.id}>
                    <Paper
                      elevation={0}
                      onClick={() => setDesignStyle(style.id)}
                      sx={{
                        p: 2,
                        border: '2px solid',
                        borderColor: designStyle === style.id ? 'primary.main' : 'divider',
                        borderRadius: 2,
                        cursor: 'pointer',
                        transition: 'all 0.2s',
                        backgroundColor: designStyle === style.id ? 'rgba(168, 85, 247, 0.08)' : 'background.paper',
                        '&:hover': {
                          borderColor: designStyle === style.id ? 'primary.main' : 'primary.light',
                          backgroundColor: designStyle === style.id ? 'rgba(168, 85, 247, 0.12)' : 'rgba(168, 85, 247, 0.04)',
                        },
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'center',
                        height: '100%',
                      }}
                    >
                      <Box sx={{ 
                        display: 'flex', 
                        gap: 0.5, 
                        mb: 1.5,
                        width: '100%',
                        justifyContent: 'center'
                      }}>
                        {style.colors.map((color, i) => (
                          <Box 
                            key={i}
                            sx={{ 
                              width: 16, 
                              height: 16, 
                              borderRadius: '50%', 
                              backgroundColor: color,
                              border: '1px solid rgba(0,0,0,0.1)'
                            }} 
                          />
                        ))}
                      </Box>
                      <Typography variant="body2" fontWeight={600} align="center" gutterBottom>
                        {style.name}
                      </Typography>
                      <Typography variant="caption" color="text.secondary" align="center">
                        {style.description}
                      </Typography>
                    </Paper>
                  </Grid>
                ))}
              </Grid>
            </Box>
          </Grid>

          {/* Layout Options */}
          <Grid item xs={12} md={6}>
            <Box sx={{ mb: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <ViewQuiltIcon sx={{ mr: 1, color: 'primary.main' }} />
                <Typography variant="subtitle1" fontWeight={600}>
                  Layout
                </Typography>
              </Box>
              
              <RadioGroup
                value={layoutOption}
                onChange={(e) => setLayoutOption(e.target.value)}
              >
                <Grid container spacing={1}>
                  {LAYOUT_OPTIONS.map((option) => (
                    <Grid item xs={12} key={option.id}>
                      <Paper
                        elevation={0}
                        sx={{
                          p: 1.5,
                          border: '1px solid',
                          borderColor: layoutOption === option.id ? 'primary.main' : 'divider',
                          borderRadius: 1,
                          backgroundColor: layoutOption === option.id ? 'rgba(168, 85, 247, 0.08)' : 'background.paper',
                        }}
                      >
                        <FormControlLabel
                          value={option.id}
                          control={<Radio />}
                          label={
                            <Box>
                              <Typography variant="body2" fontWeight={600}>
                                {option.name}
                              </Typography>
                              <Typography variant="caption" color="text.secondary">
                                {option.description}
                              </Typography>
                            </Box>
                          }
                          sx={{ width: '100%', m: 0 }}
                        />
                      </Paper>
                    </Grid>
                  ))}
                </Grid>
              </RadioGroup>
            </Box>
          </Grid>

          {/* Color Scheme */}
          <Grid item xs={12} md={6}>
            <Box sx={{ mb: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <ColorLensIcon sx={{ mr: 1, color: 'primary.main' }} />
                <Typography variant="subtitle1" fontWeight={600}>
                  Color Scheme
                </Typography>
              </Box>
              
              <RadioGroup
                value={colorScheme}
                onChange={(e) => setColorScheme(e.target.value)}
              >
                <Grid container spacing={1}>
                  {COLOR_SCHEMES.map((scheme) => (
                    <Grid item xs={12} key={scheme.id}>
                      <Paper
                        elevation={0}
                        sx={{
                          p: 1.5,
                          border: '1px solid',
                          borderColor: colorScheme === scheme.id ? 'primary.main' : 'divider',
                          borderRadius: 1,
                          backgroundColor: colorScheme === scheme.id ? 'rgba(168, 85, 247, 0.08)' : 'background.paper',
                        }}
                      >
                        <FormControlLabel
                          value={scheme.id}
                          control={<Radio />}
                          label={
                            <Box>
                              <Typography variant="body2" fontWeight={600}>
                                {scheme.name}
                              </Typography>
                              <Typography variant="caption" color="text.secondary">
                                {scheme.description}
                              </Typography>
                            </Box>
                          }
                          sx={{ width: '100%', m: 0 }}
                        />
                      </Paper>
                    </Grid>
                  ))}
                </Grid>
              </RadioGroup>
            </Box>
          </Grid>

          {/* Typography Style */}
          <Grid item xs={12} md={6}>
            <Box sx={{ mb: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <FormatSizeIcon sx={{ mr: 1, color: 'primary.main' }} />
                <Typography variant="subtitle1" fontWeight={600}>
                  Typography
                </Typography>
              </Box>
              
              <RadioGroup
                value={typographyStyle}
                onChange={(e) => setTypographyStyle(e.target.value)}
              >
                <Grid container spacing={1}>
                  {TYPOGRAPHY_STYLES.map((style) => (
                    <Grid item xs={12} key={style.id}>
                      <Paper
                        elevation={0}
                        sx={{
                          p: 1.5,
                          border: '1px solid',
                          borderColor: typographyStyle === style.id ? 'primary.main' : 'divider',
                          borderRadius: 1,
                          backgroundColor: typographyStyle === style.id ? 'rgba(168, 85, 247, 0.08)' : 'background.paper',
                        }}
                      >
                        <FormControlLabel
                          value={style.id}
                          control={<Radio />}
                          label={
                            <Box>
                              <Typography variant="body2" fontWeight={600}>
                                {style.name}
                              </Typography>
                              <Typography variant="caption" color="text.secondary">
                                {style.description}
                              </Typography>
                            </Box>
                          }
                          sx={{ width: '100%', m: 0 }}
                        />
                      </Paper>
                    </Grid>
                  ))}
                </Grid>
              </RadioGroup>
            </Box>
          </Grid>

          {/* Additional Settings */}
          <Grid item xs={12} md={6}>
            <Box sx={{ mb: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <DevicesIcon sx={{ mr: 1, color: 'primary.main' }} />
                <Typography variant="subtitle1" fontWeight={600}>
                  Additional Settings
                </Typography>
              </Box>
              
              <Paper
                elevation={0}
                sx={{
                  p: 2,
                  border: '1px solid',
                  borderColor: 'divider',
                  borderRadius: 1,
                }}
              >
                <Box sx={{ 
                  display: 'flex', 
                  justifyContent: 'space-between', 
                  alignItems: 'center',
                  mb: 2
                }}>
                  <Box>
                    <Typography variant="body2" fontWeight={600}>
                      Responsive Design
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      Optimize for all screen sizes
                    </Typography>
                  </Box>
                  <Switch
                    checked={responsiveDesign}
                    onChange={(e) => setResponsiveDesign(e.target.checked)}
                    color="primary"
                  />
                </Box>

                <Box sx={{ mt: 3 }}>
                  <Typography variant="body2" fontWeight={600} gutterBottom>
                    Animations Level: {animationsLevel === 0 ? 'None' : animationsLevel === 1 ? 'Subtle' : 'Moderate'}
                  </Typography>
                  <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mb: 1.5 }}>
                    Control the amount of animations in the design
                  </Typography>
                  <Slider
                    value={animationsLevel}
                    onChange={(e, newValue) => setAnimationsLevel(newValue)}
                    step={1}
                    marks
                    min={0}
                    max={2}
                    valueLabelDisplay="off"
                  />
                  <Box sx={{ 
                    display: 'flex', 
                    justifyContent: 'space-between', 
                    mt: 0.5 
                  }}>
                    <Typography variant="caption" color="text.secondary">None</Typography>
                    <Typography variant="caption" color="text.secondary">Subtle</Typography>
                    <Typography variant="caption" color="text.secondary">Moderate</Typography>
                  </Box>
                </Box>
              </Paper>
            </Box>
          </Grid>
        </Grid>
      </DialogContent>

      <DialogActions sx={{ px: 3, pb: 3 }}>
        <Button onClick={onClose} color="inherit">
          Cancel
        </Button>
        <Button
          onClick={handleSave}
          variant="contained"
          color="primary"
        >
          Save Design Settings
        </Button>
      </DialogActions>
    </Dialog>
  );
}

export default DesignerSettings;
